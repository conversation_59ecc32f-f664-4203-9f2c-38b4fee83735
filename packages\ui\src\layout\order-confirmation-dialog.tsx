"use client";

import * as React from "react";
import { Button } from "@workspace/ui/components/button";
import {
  Dialog,
  DialogClose,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@workspace/ui/components/dialog";
import { Separator } from "@workspace/ui/components/separator";
import { LifeBuoy } from "lucide-react";
// @ts-ignore
import Image from "next/image";

export interface SummaryItem {
  name: string;
  sodaAmount: string;
}

export interface ServiceModifier {
  name: string;
  sodaAmount: string;
}

export interface OrderConfirmationDialogProps {
  // Dialog state
  open?: boolean;
  onOpenChange?: (open: boolean) => void;

  // Text content props with defaults
  title?: string;
  username?: string;
  unitCount?: number;
  unitType?: string;
  serviceTitle?: string;

  // Data props
  avatar?: {
    src: string;
    alt?: string;
  };
  activity?: {
    src: string;
    alt?: string;
  };

  // Summary data
  mainItem: SummaryItem;
  serviceModifiers?: ServiceModifier[];
  totalSodaAmount?: number;

  // Button text
  submitButtonText?: string;
  supportButtonAriaLabel?: string;

  // Event handlers
  onSubmit?: () => void;
  onSupportClick?: () => void;
}

export function OrderConfirmationDialog({
  open = true,
  onOpenChange,
  mainItem,
  title = "Siparişini Onayla",
  username = "Haku-chan",
  unitCount = 3,
  unitType = "saatlik",
  serviceTitle = "Boost",
  avatar = {
    src: "",
    alt: "avatar",
  },
  activity = {
    src: "",
    alt: "activity",
  },
  serviceModifiers = [],
  totalSodaAmount = 0,
  submitButtonText = "GÖNDER",
  supportButtonAriaLabel = "SÜRECİ GÖSTER",
  onSubmit,
  onSupportClick,
}: OrderConfirmationDialogProps) {
  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <form>
        <DialogContent className="sm:max-w-[512px]">
          <DialogHeader>
            <DialogTitle>{title}</DialogTitle>
            <DialogDescription className="flex gap-5">
              <span className="flex shrink-0 items-start relative">
                <span className="border-3 rounded-sm overflow-hidden border-background ring-foreground ring-2">
                  <Image
                    src={avatar.src}
                    alt={avatar.alt || "avatar"}
                    width={90}
                    height={90}
                  />
                </span>
              </span>
              <span className="flex flex-col justify-between gap-2 pr-1">
                <span id="description-text" className="pt-2.5 pr-1">
                  {username}'dan {unitCount} {unitType} sipariş vermek üzeresin.
                </span>
                <span className="flex justify-end items-center gap-2.5 text-foreground">
                  {serviceTitle} x{unitCount}
                  <Image
                    src={activity.src}
                    alt={activity.alt || "activity"}
                    width={30}
                    height={30}
                    className="rounded-sm border-2 border-background ring-foreground ring-2"
                  />
                </span>
              </span>
            </DialogDescription>
          </DialogHeader>

          <section
            id="summary"
            className="[--ring:var(--background)] fake-stroke rounded-lg bg-muted text-sm text-muted-foreground mx-7 py-5 pl-5"
          >
            <div className="overflow-y-auto max-h-44 space-y-3 pr-5">
              <ul className="">
                <li className="flex justify-between items-center">
                  <span>{mainItem.name}</span>
                  <span>
                    {mainItem.sodaAmount} soda x{unitCount}
                  </span>
                </li>
              </ul>
              {serviceModifiers && serviceModifiers.length > 0 && (
                <>
                  <Separator />
                  <ul className="space-y-1">
                    {serviceModifiers.map((item, index) => (
                      <li key={index} className="flex justify-between">
                        <span>{item.name}</span>
                        <span>
                          {item.sodaAmount} soda x{unitCount}
                        </span>
                      </li>
                    ))}
                  </ul>
                </>
              )}
            </div>
            <div className="space-y-2 pr-5">
              <Separator />
              <ul className="text-foreground text-lg">
                <li className="flex justify-between">
                  <span>{"Toplam"}</span>
                  <span>{totalSodaAmount + " soda"}</span>
                </li>
              </ul>
            </div>
          </section>

          <DialogFooter>
            <Button size={"icon"} onClick={onSupportClick}>
              <LifeBuoy className="size-9" />
              <p className="sr-only">{supportButtonAriaLabel}</p>
            </Button>
            <DialogClose asChild>
              <Button variant={"primary"} onClick={onSubmit}>
                {submitButtonText}
              </Button>
            </DialogClose>
          </DialogFooter>
        </DialogContent>
      </form>
    </Dialog>
  );
}
