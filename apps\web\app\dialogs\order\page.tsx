import { OrderConfirmationDialog } from "@workspace/ui/layout/order-confirmation-dialog";
import avatar from "./avatar.png";
import activity from "./activity.jpg";

export default async function Page() {
  return (
    <OrderConfirmationDialog
      open={true}
      username="<PERSON><PERSON>-chan"
      unitCount={3}
      unitType="saatlik"
      serviceTitle="Boost"
      avatar={{
        src: avatar.src,
        alt: "avatar",
      }}
      activity={{
        src: activity.src,
        alt: "activity",
      }}
      mainItem={{
        name: "Volarant - Boost",
        sodaAmount: "52",
      }}
      serviceModifiers={[
        { name: "Maid Cosplay", sodaAmount: "10" },
        { name: "<PERSON><PERSON><PERSON>", sodaAmount: "2" },
        { name: "<PERSON><PERSON>", sodaAmount: "1" },
        { name: "<PERSON> Carrey Taklidi", sodaAmount: "15" },
      ]}
      totalSodaAmount={240}
    />
  );
}
